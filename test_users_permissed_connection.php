<?php
// 测试 users_permissed.php 的 MongoDB 连接

// MongoDB 連接設置
require_once 'vendor/autoload.php';
require_once 'vendor/vlucas/phpdotenv/src/Dotenv.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "=== MongoDB Connection Test for users_permissed.php ===\n";

$mongoHost = $_ENV['MONGO_HOST'];
$mongoPort = $_ENV['MONGO_PORT'];
$mongoUser = $_ENV['MONGO_USER'];
$mongoPass = $_ENV['MONGO_PASS'];
$mongoDb = $_ENV['MONGO_DB_USER']; // 使用 optools 數據庫
$mongoAuth = $_ENV['MONGO_AUTH_DB'];

echo "Host: $mongoHost\n";
echo "Port: $mongoPort\n";
echo "User: $mongoUser\n";
echo "Database: $mongoDb\n";
echo "Auth DB: $mongoAuth\n";

$mongoUri = "mongodb://{$mongoUser}:{$mongoPass}@{$mongoHost}:{$mongoPort}/{$mongoDb}?authSource={$mongoAuth}";
echo "Connection URI: $mongoUri\n\n";

$mongodb_connected = false;
$mongodb_error = '';
$client = null;
$collection = null;

try {
    echo "Attempting to connect to MongoDB...\n";
    $client = new MongoDB\Client($mongoUri);
    
    echo "Testing connection with listDatabases()...\n";
    $databases = $client->listDatabases();
    
    echo "Setting up collection...\n";
    $collection = $client->$mongoDb->users;
    
    echo "Testing user count...\n";
    $user_count = $collection->countDocuments();
    echo "User count: $user_count\n";
    
    $mongodb_connected = true;
    echo "✅ MongoDB connection successful!\n";
    
} catch (Exception $e) {
    $mongodb_error = 'MongoDB connection failed: ' . $e->getMessage();
    echo "❌ MongoDB connection failed: " . $e->getMessage() . "\n";
    echo "Error details: " . $e->getTraceAsString() . "\n";
}

// 测试 GET 请求处理
if ($mongodb_connected) {
    echo "\n=== Testing GET request simulation ===\n";
    try {
        // 模拟 GET 请求
        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_GET['action'] = 'get_users';
        
        echo "Fetching users from MongoDB...\n";
        $cursor = $collection->find();
        $users_data = [];

        foreach ($cursor as $doc) {
            $user = [];
            foreach ($doc as $key => $value) {
                if ($value instanceof MongoDB\BSON\ObjectId) {
                    $user[$key] = (string) $value;
                } elseif ($value instanceof MongoDB\BSON\UTCDateTime) {
                    $user[$key] = $value->toDateTime()->format('Y-m-d H:i:s');
                } else {
                    $user[$key] = $value;
                }
            }
            
            // 確保 function 字段存在
            if (!isset($user['function'])) {
                $user['function'] = [];
            }
            
            $users_data[] = $user;
        }
        
        echo "Found " . count($users_data) . " users\n";
        echo "Sample user data:\n";
        if (!empty($users_data)) {
            echo json_encode($users_data[0], JSON_PRETTY_PRINT) . "\n";
        }
        
        echo "✅ GET request simulation successful!\n";
        
    } catch (Exception $e) {
        echo "❌ GET request simulation failed: " . $e->getMessage() . "\n";
    }
}
?>
