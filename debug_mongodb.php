<?php
header('Content-Type: application/json');

$debug_info = [
    'step' => 'start',
    'errors' => []
];

try {
    // 步骤 1: 检查 autoload
    $debug_info['step'] = 'checking_autoload';
    if (!file_exists('vendor/autoload.php')) {
        throw new Exception('vendor/autoload.php not found');
    }
    $debug_info['autoload_exists'] = true;

    // 步骤 2: 加载 autoload
    $debug_info['step'] = 'loading_autoload';
    require_once 'vendor/autoload.php';
    $debug_info['autoload_loaded'] = true;

    // 步骤 3: 检查 .env 文件
    $debug_info['step'] = 'checking_env_file';
    if (!file_exists('.env')) {
        throw new Exception('.env file not found');
    }
    $debug_info['env_file_exists'] = true;

    // 步骤 4: 检查 Dotenv 类
    $debug_info['step'] = 'checking_dotenv_class';
    if (!class_exists('Dotenv\Dotenv')) {
        throw new Exception('Dotenv\Dotenv class not found');
    }
    $debug_info['dotenv_class_exists'] = true;

    // 步骤 5: 加载环境变量
    $debug_info['step'] = 'loading_env_vars';
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
    $dotenv->load();
    $debug_info['env_vars_loaded'] = true;

    // 步骤 6: 检查环境变量
    $debug_info['step'] = 'checking_env_vars';
    $required_vars = ['MONGO_HOST', 'MONGO_PORT', 'MONGO_USER', 'MONGO_PASS', 'MONGO_DB_USER', 'MONGO_AUTH_DB'];
    $env_vars = [];
    foreach ($required_vars as $var) {
        $env_vars[$var] = $_ENV[$var] ?? 'NOT_SET';
    }
    $debug_info['env_vars'] = $env_vars;

    // 步骤 7: 检查 MongoDB 类
    $debug_info['step'] = 'checking_mongodb_class';
    if (!class_exists('MongoDB\Client')) {
        throw new Exception('MongoDB\Client class not found');
    }
    $debug_info['mongodb_class_exists'] = true;

    // 步骤 8: 尝试连接 MongoDB
    $debug_info['step'] = 'connecting_mongodb';
    $mongoHost = $_ENV['MONGO_HOST'];
    $mongoPort = $_ENV['MONGO_PORT'];
    $mongoUser = $_ENV['MONGO_USER'];
    $mongoPass = $_ENV['MONGO_PASS'];
    $mongoDb = $_ENV['MONGO_DB_USER'];
    $mongoAuth = $_ENV['MONGO_AUTH_DB'];

    $mongoUri = "mongodb://{$mongoUser}:{$mongoPass}@{$mongoHost}:{$mongoPort}/{$mongoDb}?authSource={$mongoAuth}";
    $debug_info['connection_uri'] = $mongoUri;

    $client = new MongoDB\Client($mongoUri);
    $debug_info['client_created'] = true;

    // 步骤 9: 测试连接
    $debug_info['step'] = 'testing_connection';
    $databases = $client->listDatabases();
    $debug_info['connection_tested'] = true;

    // 步骤 10: 测试集合
    $debug_info['step'] = 'testing_collection';
    $collection = $client->$mongoDb->users;
    $user_count = $collection->countDocuments();
    $debug_info['user_count'] = $user_count;

    $debug_info['step'] = 'success';
    $debug_info['success'] = true;

} catch (Exception $e) {
    $debug_info['success'] = false;
    $debug_info['error'] = $e->getMessage();
    $debug_info['error_trace'] = $e->getTraceAsString();
}

echo json_encode($debug_info, JSON_PRETTY_PRINT);
?>
