<?php
session_start();

if (!isset($_SESSION['user_email'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => '未登录']);
    exit;
}

header('Content-Type: application/json');

// MongoDB 連接設置
require_once 'vendor/autoload.php';
require_once 'vendor/vlucas/phpdotenv/src/Dotenv.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$mongoHost = $_ENV['MONGO_HOST'];
$mongoPort = $_ENV['MONGO_PORT'];
$mongoUser = $_ENV['MONGO_USER'];
$mongoPass = $_ENV['MONGO_PASS']; // 保持特殊字符不變
$mongoDb = $_ENV['MONGO_DB_USER']; // 使用 optools 數據庫
$mongoAuth = $_ENV['MONGO_AUTH_DB'];

$mongoUri = "mongodb://{$mongoUser}:{$mongoPass}@{$mongoHost}:{$mongoPort}/{$mongoDb}?authSource={$mongoAuth}";

try {
    $data = json_decode(file_get_contents('php://input'), true);
    $oldPassword = $data['oldPassword'];
    $newPassword = $data['newPassword'];
    $email = $_SESSION['user_email'];

    // 連接 MongoDB
    $client = new MongoDB\Client($mongoUri);
    $collection = $client->$mongoDb->users;

    // 從 MongoDB 查找用戶
    $user = $collection->findOne(['email' => $email]);

    if (!$user) {
        echo json_encode(['success' => false, 'message' => '用户不存在']);
        exit;
    }

    // 驗證舊密碼
    if (!password_verify($oldPassword, $user['password'])) {
        echo json_encode(['success' => false, 'message' => '旧密码不正确']);
        exit;
    }

    // 更新密碼
    $newPasswordHashed = password_hash($newPassword, PASSWORD_DEFAULT);
    $result = $collection->updateOne(
        ['email' => $email],
        ['$set' => ['password' => $newPasswordHashed]]
    );

    if ($result->getModifiedCount() > 0) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => '密码更新失败']);
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>

