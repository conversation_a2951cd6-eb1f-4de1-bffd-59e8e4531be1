<?php
session_start(); // 开始会话

// 設置默認字符集為 UTF-8
ini_set('default_charset', 'UTF-8');

if (!isset($_SESSION['user_email'])) {
    header('Location: login.php'); // 如果未登录，则重定向到登录页面
    exit; // 停止执行后续代码
}

// 防止缓存,為了防止當逾時被登出時，重新登入後造成網頁出現重複選單
header("Cache-Control: no-cache, must-revalidate"); // HTTP/1.1
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT"); // 过去的日期
header("Pragma: no-cache"); // HTTP/1.0

// 設置內容類型為 UTF-8
header('Content-Type: text/html; charset=UTF-8');

// MongoDB 連接設置
$mongodb_connected = false;
$mongodb_error = '';
$usersData = [];

try {
    // 加載 Composer autoload
    if (!file_exists('vendor/autoload.php')) {
        throw new Exception('Composer autoload not found');
    }
    require_once 'vendor/autoload.php';

    // 加載環境變量
    if (!class_exists('Dotenv\Dotenv')) {
        throw new Exception('Dotenv class not found');
    }

    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
    $dotenv->load();

    // 檢查必要的環境變量
    $required_vars = ['MONGO_HOST', 'MONGO_PORT', 'MONGO_USER', 'MONGO_PASS', 'MONGO_DB_USER', 'MONGO_AUTH_DB'];
    foreach ($required_vars as $var) {
        if (!isset($_ENV[$var])) {
            throw new Exception("Environment variable $var not found");
        }
    }

    $mongoHost = $_ENV['MONGO_HOST'];
    $mongoPort = $_ENV['MONGO_PORT'];
    $mongoUser = $_ENV['MONGO_USER'];
    $mongoPass = $_ENV['MONGO_PASS'];
    $mongoDb = $_ENV['MONGO_DB_USER']; // 使用 optools 數據庫
    $mongoAuth = $_ENV['MONGO_AUTH_DB'];

    $mongoUri = "mongodb://{$mongoUser}:{$mongoPass}@{$mongoHost}:{$mongoPort}/{$mongoDb}?authSource={$mongoAuth}";

    // 檢查 MongoDB 類是否存在
    if (!class_exists('MongoDB\Client')) {
        throw new Exception('MongoDB\Client class not found - MongoDB PHP driver may not be installed');
    }

    $client = new MongoDB\Client($mongoUri);
    $client->listDatabases(); // 測試連接
    $collection = $client->$mongoDb->users; // 使用 'users' 集合
    $mongodb_connected = true;
    error_log("MongoDB 連接成功 (index.php)");

    // 從 MongoDB 讀取所有用戶數據
    $cursor = $collection->find();
    foreach ($cursor as $doc) {
        $user = [];
        foreach ($doc as $key => $value) {
            if ($value instanceof MongoDB\BSON\ObjectId) {
                $user[$key] = (string) $value;
            } elseif ($value instanceof MongoDB\BSON\UTCDateTime) {
                $user[$key] = $value->toDateTime()->format('Y-m-d H:i:s');
            } else {
                $user[$key] = $value;
            }
        }

        // 確保 function 字段存在
        if (!isset($user['function'])) {
            $user['function'] = [];
        }

        $usersData[] = $user;
    }

} catch (Exception $e) {
    $mongodb_error = 'MongoDB setup failed: ' . $e->getMessage();
    error_log($mongodb_error);
    // 如果 MongoDB 連接失敗，設置空的用戶數據
    $usersData = [];
}

// 获取当前登录用户的 email
$currentUserEmail = $_SESSION['user_email'];

// 初始化用户功能配置
$userFunctions = [];
$userRole = 'user'; // 默認角色

// 查找当前用户的数据
foreach ($usersData as $user) {
    if ($user['email'] === $currentUserEmail) {
        $userFunctions = $user['function'] ?? [];
        $userRole = $user['role'] ?? 'user'; // 获取用户角色，默認為 user
        break;
    }
}

// 如果未找到用户或未定义功能，设置默认值
if (empty($userFunctions)) {
    $userFunctions = [];
}

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OP TOOL</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

    <link rel=stylesheet type="text/css" href="index.css">

    <script src="monitor.js"></script>

    <style>
        /* 在 style 中添加 */
        .modal.logout-notification {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 2000;
        }

        .modal.logout-notification .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 300px;
            text-align: center;
            border-radius: 5px;
        }

        .btn-confirm {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 15px;
        }

        .btn-logout {
            padding: 7px 15px;
            font-size: 14px;
            background-color: #f44336;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            display: inline-block;
            transition: background-color 0.3s;
        }

        .btn-logout:hover {
            background-color: #c33d2e;
            cursor: pointer;
        }

        .top-bar {
            background-color: #222;
            /* 确保顶部栏颜色与侧边栏颜色一致 */
            color: white;
            padding: 16px;
            /* 根据需要调整 */
            position: fixed;
            top: 0;
            width: 100%;
            height: 60px;
            /* 根据需要调整 */
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .top-bar .user-info {
            position: relative;
            display: inline-block;
            margin-right: 20px;
        }

        /* 新增用戶名稱樣式 */
        .top-bar .user-info span:first-child {
            font-size: 18px;
            /* 加大字體 */
            font-weight: bold;
            /* 粗體 */
            color: #ffeb3b;
            /* 黃色 */
        }

        .top-bar .user-info:hover .dropdown-content {
            display: block;
        }

        .top-bar .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            background-color: #333;
            /* 修改颜色以匹配顶部栏 */
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
        }

        .top-bar .dropdown-content a {
            color: white;
            /* 修改文字颜色以匹配顶部栏 */
            padding: 12px 16px;
            text-decoration: none;
            display: block;
        }

        .top-bar .dropdown-content a:hover {
            background-color: #575757;
            /* 修改悬停背景颜色以匹配顶部栏 */
        }

        .sidebar-left {
            background-color: #222;
            /* 确保侧边栏颜色与顶部栏颜色一致 */
            position: fixed;
            top: 0;
            /* 覆盖顶部栏 */
            left: 0;
            width: 250px;
            height: 100%;
            /* overflow-y: auto; */
            /* 允许垂直滚动 */
            padding-top: 60px;
            /* 根据顶部栏高度调整 */
            z-index: 1100;
            /* 确保侧边栏的层级高于顶部栏 */
            scrollbar-width: none;
            /* Firefox: 隐藏滚动条 */
            border: none;
            /* 移除任何邊框，不然預設會是黑色，會跟其他iframe子網頁合不起來 */
            border-radius: 0;
            /* 移除圓角，不然會跟其他iframe子網頁合不起來 */
        }

        .sidebar-left::-webkit-scrollbar {
            width: 0;
            /* Safari 和 Chrome: 隐藏滚动条 */
        }

        .sidebar-left::-webkit-scrollbar-thumb {
            background-color: #444;
            border-radius: 5px;
        }

        .sidebar-left::-webkit-scrollbar-thumb:hover {
            background-color: #555;
        }

        .sidebar-left::-webkit-scrollbar-track {
            background-color: #222;
        }

        /* CSS重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body,
        html {
            height: 100%;
            width: 100%;
            overflow: hidden;
            /* 确保没有滚动条 */
        }

        #content {
            position: absolute;
            /* 使用绝对定位确保充满整个页面 */
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
        }

        iframe {
            border: none;
            /* 移除边框 */
            margin: 0;
            /* 移除外边距 */
            padding: 0;
            /* 移除内边距 */
            /*    display: block; */
            /* 确保iframe是块级元素 */
            width: 100%;
            /* 使iframe充满父容器 */
            height: 100%;
            /* 使iframe充满父容器 */
        }

        .content {
            margin-left: 270px;
            /* 调整基于侧边栏的宽度 */
            margin-top: 70px;
            /* 调整基于顶部栏的高度 */
            padding: 20px;
            height: calc(100vh - 60px);
            /* 這是設terminal網頁的下邊距高度(包含左bar與主機效能提取bar)，修改60px，70或50px都太高或太低 */
            overflow: auto;
            /* 允许在需要时出现滚动条 */
        }

        #iframeContent {
            width: 100%;
            height: 100%;
            /* 使 iframe 填满 .content 区域 */
            border: none;
        }

        /* 隐藏 iframe 的滚动条 */
        #iframeContent {
            overflow: hidden;
        }

        .modal {
            display: none;
            /* Hidden by default */
            position: fixed;
            /* Stay in place */
            z-index: 1000;
            /* Sit on top */
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            /* Enable scroll if needed */
            background-color: rgb(0, 0, 0);
            /* Fallback color */
            background-color: rgba(0, 0, 0, 0.4);
            /* Black w/ opacity */
            padding-top: 60px;
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            /* Centered */
            padding: 20px;
            border: 1px solid #888;
            width: 25%;
            /* Adjust width as necessary */
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        .show-password {
            cursor: pointer;
        }
    </style>

</head>

<body>

    <!-- 在 body 中添加 -->
    <div id="logoutModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h2>系統通知</h2>
            <p id="logoutMessage"></p>
            <button onclick="confirmLogout()" class="btn-confirm">確定</button>
        </div>
    </div>

    <div class="top-bar">
        <div></div> <!-- Empty div to push the user-info to the right -->
        <div class="user-info">
            <span><?php echo $_SESSION['user_email']; ?></span>
            <span> | Session ID: <?php echo session_id(); ?></span>
            <div class="dropdown-content">
                <a href="#" id="ChangePassword" onclick="showChangePasswordModal()">修改密碼</a>
                <a id="logout" href="#" onclick="logout()">登出</a>
            </div>
        </div>
    </div>

    <div id="changePasswordModal" class="modal">
        <div class="modal-content">
            <span class="close" id="closeChangePassword" onclick="closeChangePasswordModal()">&times;</span>
            <h2>修改密碼</h2>
            <form id="changePasswordForm">
                <label for="oldPassword">舊密碼:</label>
                <input type="password" id="oldPassword" name="oldPassword" required>
                <span class="show-password" onclick="togglePasswordVisibility('oldPassword')">顯示</span><br><br>
                <label for="newPassword">新密碼:</label>
                <input type="password" id="newPassword" name="newPassword" required>
                <span class="show-password" onclick="togglePasswordVisibility('newPassword')">顯示</span><br><br>
                <button id="change-password-submit" type="submit">確定</button>
            </form>
            <p id="passwordCapsLockWarning" style="color: red; display: none;">注意: 大小寫鎖定開啟</p>
        </div>
    </div>

    <div class='wrapper'>
        <aside class="sidebar-left navbar navbar-inverse">
            <div class="navbar-header">
                <a class="navbar-brand" id="navbar-brand" href="index.php">OP TOOL</a>
                <input type="text" id="globalSearch" placeholder="請搜尋需要的功能項..." style="margin-top: 10px; padding: 5px; width: 200px;">
            </div>
            <nav class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
                <ul class="sidebar-menu">
                    <li class="header"></li>
                    <?php
                    // 遍历四个主要类别
                    foreach ($userFunctions as $categoryName => $subFunctions) {
                        // 如果类别是 '後台管理'，且用户不是管理员，跳过该类别
                        if ($categoryName === '後台管理' && $userRole !== 'admin') {
                            continue;
                        }

                        // 对于 '後台管理'，如果用户是管理员，无论子功能是否启用，都显示主类别
                        if ($categoryName === '後台管理' && $userRole === 'admin') {
                            $hasEnabledFunction = true; // 强制显示主类别
                        } else {
                            // 检查子功能是否为空或全部被禁用
                            $hasEnabledFunction = false;
                            foreach ($subFunctions as $functionName => $functionDetails) {
                                if ($functionDetails[1] === true) {
                                    $hasEnabledFunction = true;
                                    break;
                                }
                            }
                        }

                        // 如果该类别下没有启用的功能，且不是管理员的 '後台管理'，跳过
                        if (!$hasEnabledFunction) {
                            continue;
                        }
                    ?>
                        <li class="treeview">
                            <a href="#" class="expandable" id="<?php echo strtolower($categoryName); ?>">
                                <i class="fa fa-cogs"></i>
                                <!-- <span><?php echo htmlspecialchars($categoryName); ?></span> -->
                                <span><?php echo htmlspecialchars($categoryName, ENT_QUOTES, 'UTF-8'); ?></span>

                            </a>
                            <ul class="nested">
                                <?php
                                // 遍历子功能
                                foreach ($subFunctions as $functionName => $functionDetails) {
                                    $url = $functionDetails[0];
                                    $enabled = $functionDetails[1];
                                    if ($enabled) {
                                ?>
                                        <li>
                                            <a href="#" id="<?php echo strtolower($categoryName) . '-' . strtolower($functionName); ?>" onclick="loadContent('<?php echo htmlspecialchars($url); ?>')">
                                                <!-- <?php echo htmlspecialchars($functionName); ?> -->
                                                <?php echo htmlspecialchars($functionName, ENT_QUOTES, 'UTF-8'); ?>
                                            </a>
                                        </li>
                                <?php
                                    }
                                }
                                ?>
                            </ul>
                        </li>
                    <?php
                    }
                    ?>

                </ul>
            </nav>
        </aside>

        <div class='content container-fluid' id="content">
            <!--<h3><b>善用工具哈！</b></h3> -->
            <div class="panel-body" id="chickensoup">
                <img src="thor.jpg">
                <p></p>
                <p></p>
                <p>每個人都有一個強大的潛力，只要你開始行動，你就會發現你能夠做到的事情遠遠超過你所想象的。</p>
                <p>成功不是一個終點，而是一個旅程。繼續向前走，一步一步地向著你的目標邁進。</p>
            </div>

            <div id="content">
                <iframe id="iframeContent" name="iframeContent" src="about:blank"></iframe>

            </div>
        </div>

        <script>
            let isLoggingOut = false; // 在這裡定義全局變量
            let logoutCheckInterval; // 定義檢查間隔變量


            // 添加全局的確認登出函數
            function confirmLogout() {
                console.log('Confirm logout clicked');
                if (!isLoggingOut) return;

                const modal = document.getElementById('logoutModal');
                modal.style.display = 'none';

                console.log('Executing immediate logout after confirmation');
                performLogout();
            }

            // 修改 performLogout 函數
            function performLogout() {
                console.log('Performing logout function');

                // 清除本地存储的 JWT token
                localStorage.removeItem('jwtToken');

                // 清除會話並跳轉
                fetch('logout.php', {
                        method: 'POST',
                        credentials: 'include'
                    })
                    .then(response => {
                        console.log('Logout response received:', response);
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Logout successful, redirecting to login page');
                        window.top.location.href = 'login.php';
                    })
                    .catch(error => {
                        console.error('Error during logout:', error);
                        window.top.location.href = 'login.php';
                    });
            }

            function loadContent(url) {
                const jwtToken = localStorage.getItem('jwtToken');
                if (jwtToken) {
                    url += (url.includes('?') ? '&' : '?') + 'jwt=' + encodeURIComponent(jwtToken);
                }

                // 将 user_email 添加到 URL
                if (url !== 'index.php') {
                    url += (url.includes('?') ? '&' : '?') + 'user_email=<?php echo urlencode($_SESSION['user_email'] ?? ''); ?>';
                }

                if (url !== 'index.php') {
                    document.getElementById('chickensoup').style.display = 'none';
                } else {
                    document.getElementById('chickensoup').style.display = 'block';
                }

                const iframeContent = document.getElementById('iframeContent');
                iframeContent.src = url;

                // 修改 .content 樣式
                const contentDiv = document.querySelector('.content');

                // 如果是  terminal.php 網頁，移除上間距和左間距
                // 等待 iframe 加載完成
                iframeContent.onload = function() {
                    //const iframePath = iframeContent.contentWindow.location.pathname;

                    // 如果是  terminal.php，移除 margin 和 padding
                    if (url.includes('terminal.php')) {
                        contentDiv.style.marginLeft = '250px';
                        contentDiv.style.marginTop = '60px';
                        contentDiv.style.padding = '20px';
                    } else {
                        contentDiv.style.marginLeft = '270px';
                        contentDiv.style.marginTop = '70px';
                        contentDiv.style.padding = '20px';
                    }
                };
            }

            function showChangePasswordModal() {
                document.getElementById('changePasswordModal').style.display = 'block';
            }

            function closeChangePasswordModal() {
                document.getElementById('changePasswordModal').style.display = 'none';
            }

            function togglePasswordVisibility(id) {
                var passwordField = document.getElementById(id);
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                } else {
                    passwordField.type = 'password';
                }
            }

            document.getElementById('changePasswordForm').onsubmit = function(event) {
                event.preventDefault();
                var oldPassword = document.getElementById('oldPassword').value;
                var newPassword = document.getElementById('newPassword').value;

                // 使用 fetch API 发送请求到 PHP 脚本
                fetch('change_password.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            oldPassword: oldPassword,
                            newPassword: newPassword
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('密碼修改成功');
                            closeChangePasswordModal();
                        } else {
                            alert('密碼修改失敗: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('錯誤:', error);
                    });
            };

            document.addEventListener('DOMContentLoaded', function() {
                var expandables = document.querySelectorAll('.expandable');
                expandables.forEach(function(expandable) {
                    expandable.addEventListener('click', function() {
                        this.classList.toggle('active');
                        var nested = this.nextElementSibling;
                        nested.style.display = nested.style.display === 'block' ? 'none' : 'block';
                    });
                });

                var nestedLists = document.querySelectorAll('.nested');
                nestedLists.forEach(function(list) {
                    list.style.display = 'none'; // Initialize all lists as closed
                });

                var globalSearch = document.getElementById('globalSearch');
                globalSearch.addEventListener('input', function() {
                    var searchTerm = this.value.toLowerCase();
                    var allLinks = document.querySelectorAll('.nested a');

                    if (!searchTerm) {
                        // 搜索框為空，恢復所有項目為可見，收合所有樹狀結構並移除 active 狀態
                        allLinks.forEach(function(link) {
                            link.style.display = 'block'; // 恢復所有鏈接為可見
                        });
                        nestedLists.forEach(function(list) {
                            list.style.display = 'none';
                        });
                        expandables.forEach(function(expand) {
                            expand.classList.remove('active');
                        });
                    } else {
                        // 有搜索詞時處理顯示
                        allLinks.forEach(function(link) {
                            var text = link.textContent.toLowerCase();
                            if (text.includes(searchTerm)) {
                                link.style.display = 'block';
                                link.closest('ul').style.display = 'block';
                                link.closest('.treeview').querySelector('.expandable').classList.add('active');
                            } else {
                                link.style.display = 'none';
                            }
                        });
                    }
                });

                var passwordFields = document.querySelectorAll('#oldPassword, #newPassword');
                passwordFields.forEach(function(field) {
                    field.addEventListener('keyup', function(event) {
                        var capsLockWarning = document.getElementById('passwordCapsLockWarning');
                        if (event.getModifierState('CapsLock')) {
                            capsLockWarning.style.display = 'block';
                        } else {
                            capsLockWarning.style.display = 'none';
                        }
                    });
                });

                var links = document.querySelectorAll('.sidebar-menu a');
                links.forEach(function(link) {
                    link.addEventListener('click', function(event) {
                        event.preventDefault();
                        var onclickAttr = this.getAttribute('onclick');
                        if (onclickAttr) {
                            var urlMatch = onclickAttr.match(/'([^']+)'/);
                            if (urlMatch) {
                                var url = urlMatch[1];
                                loadContent(url);
                            }
                        }
                    });
                });

            });

            //登出功能
            function logout() {
                fetch('logout.php', {
                        method: 'POST',
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 重定向到登录页面
                            if (window.top !== window.self) {
                                window.top.location.href = 'login.php';
                            } else {
                                window.location.href = 'login.php';
                            }
                        } else {
                            alert('登出失敗');
                        }
                    })
                    .catch(error => {
                        console.error('登出時發生錯誤:', error);
                    });
            }

            //透過websocket來處理登出功能,可讓用戶隨時登出(websocket需要在後台執行
            const sessionId = '<?php echo session_id(); ?>';
            let ws;

            // ! 限定用戶只能用一個瀏覽器做登入(多的會被登出)的流程
            // ! 1. 用戶A在瀏覽器1登入，創建新的 session 記錄
            // ! 2. 用戶A在瀏覽器2登入時，authenticate.php 移除舊的 session 記錄，這是authenticate.php的代碼
            //foreach ($online_users as $key => $online_user) {
            //     if ($online_user['email'] === $email) {
            //         unset($online_users[$key]);
            //     }
            // }
            // ! 3. 瀏覽器1中的 checkLogoutStatus 定時檢查發現自己的 session 已被移除
            // ! 4. 執行 performLogout() 將用戶登出並返回登入頁面
            // + 這函數目前暫時先不使用，因為已經做到可以立即登出了
            // function checkLogoutStatus() {
            //     if (isLoggingOut) {
            //         return; // 如果正在處理登出，不執行檢查
            //     }

            //     fetch('check_logout_status.php', {
            //             method: 'GET',
            //             credentials: 'include'
            //         })
            //         .then(response => {
            //             if (!response.ok) {
            //                 throw new Error('Network response was not ok');
            //             }
            //             return response.json();
            //         })
            //         .then(data => {
            //             if (data.is_logged_out && !isLoggingOut) {
            //                 isLoggingOut = true;
            //                 console.log('Logout status detected');
            //                 // 顯示登出提示
            //                 const modal = document.getElementById('logoutModal');
            //                 const messageElement = document.getElementById('logoutMessage');
            //                 messageElement.textContent = '你已被登出系統';
            //                 modal.style.display = 'block';

            //                 console.log('Starting 15 second countdown before logout');
            //                 setTimeout(() => {
            //                     console.log('Executing delayed logout');
            //                     performLogout();
            //                 }, 15000);
            //             }
            //         })
            //         .catch(error => {
            //             console.error('Error checking logout status:', error);
            //         });
            // }


            // WebSocket 连接和消息处理
            // ! 這必須連到websocket_server.php(後台運作中，已改成systemctl執行)，若這服務停掉，則線上管控的登出online.php的登出用戶功能會失敗
            function connectWebSocket() {
                ws = new WebSocket('wss://op-tool.reaix.com:8080');

                ws.onopen = () => {
                    console.log('WebSocket connected');
                    ws.send(JSON.stringify({
                        session_id: sessionId
                    }));
                };

                ws.onmessage = (event) => {
                    try {
                        const message = JSON.parse(event.data);
                        //console.log('Raw WebSocket message:', event.data);
                        console.log('Parsed message:', message);

                        // 檢查是否是登出消息
                        if (message.type === 'logout' && !isLoggingOut) {
                            console.log('Processing logout message...');
                            isLoggingOut = true;

                            const modal = document.getElementById('logoutModal');
                            const messageElement = document.getElementById('logoutMessage');

                            // 根據登出原因設置消息
                            let logoutMessage;
                            switch (message.reason) {
                                case 'admin_logout':
                                    logoutMessage = '您已被管理者登出！';
                                    console.log('Admin logout detected');
                                    break;
                                case 'browser_replaced':
                                    logoutMessage = '您已從其他瀏覽器登入！';
                                    console.log('Browser replacement detected');
                                    break;
                                default:
                                    logoutMessage = '您已登出系統';
                                    console.log('Default logout message used');
                            }

                            console.log('Setting logout message:', logoutMessage);
                            messageElement.textContent = logoutMessage;
                            modal.style.display = 'block';
                            document.body.style.pointerEvents = 'none';
                            modal.style.pointerEvents = 'auto';

                            console.log('Starting delayed logout timer...');
                            setTimeout(() => {
                                if (modal.style.display !== 'none') { // 在登出操作完成或用户确认后，设置 modal.style.display = 'none'; 隐藏模态窗口。
                                    console.log('Executing delayed logout');
                                    performLogout();
                                }
                            }, 20000); // ! 這邊是設定當用戶被通知登出後，停留在當前畫面多久時間後，才會回到login.php (設20秒)
                        } else if (message.type === 'session_id_request') {
                            console.log('Received session ID request');
                            ws.send(JSON.stringify({
                                session_id: sessionId
                            }));
                        }
                    } catch (error) {
                        console.error('Error processing WebSocket message:', error);
                        console.error('Raw message:', event.data);
                    }
                };

                ws.onclose = () => {
                    console.log('WebSocket disconnected. Attempting to reconnect...');
                    setTimeout(connectWebSocket, 5000);
                };

                ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                };
            }

            // 初始连接
            connectWebSocket();

            // ! 每 5 秒检查一次登出状态,當我在線上用戶管理介面將用戶登出時,需加入此代碼讓網頁每五秒檢查一次,當收到websocket傳來的登出,才能重整網頁讓帳號被登出
            // + 目前暫時先不使用，因為已經做到可以立即登出了
            //setInterval(checkLogoutStatus, 5000);
        </script>
</body>

</html>



